import os
import openai
from dotenv import load_dotenv
from datetime import datetime

# 加载环境变量
load_dotenv()

# 从环境中获取 OpenAI API 密钥和自定义配置
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL")
MODEL_NAME = os.getenv("MODEL", "gemini-2.5-flash") # 用户指定 gemini-2.5-flash

if not OPENAI_API_KEY:
    raise ValueError("请在 .env 文件中设置 OPENAI_API_KEY 环境变量。")
if not OPENAI_BASE_URL:
    raise ValueError("请在 .env 文件中设置 OPENAI_BASE_URL 环境变量。")

# 初始化 OpenAI 客户端
client = openai.OpenAI(api_key=OPENAI_API_KEY, base_url=OPENAI_BASE_URL)

def load_file_content(filepath: str) -> str:
    """加载文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误: 文件未找到 - {filepath}")
        return ""

def parse_chatlog(chatlog_content: str) -> list[dict]:
    """
    解析 chatlog 内容，提取完整的对话轮次 (User + Assistant)。
    每个对话轮次包含 'user_input' 和 'assistant_response'。
    """
    dialogues = []
    lines = chatlog_content.split('\n')
    
    # 跳过开头的元数据直到第一个 "---"
    start_parsing_index = -1
    for i in range(len(lines)):
        if lines[i].strip() == "---":
            start_parsing_index = i + 1
            break
    
    if start_parsing_index == -1:
        return []

    i = start_parsing_index

    while i < len(lines):
        user_input_lines = []
        assistant_response_lines = []
        
        # 寻找用户输入
        while i < len(lines) and not lines[i].startswith("## 🧑‍💻 User"):
            i += 1
        
        if i >= len(lines): # 文件结束或没有更多用户输入
            break
        
        # 跳过 "## 🧑‍💻 User"
        i += 1
        # 读取用户输入内容
        while i < len(lines) and not (lines[i].startswith("---") or lines[i].startswith("## 🤖 Assistant")):
            if lines[i].strip():
                user_input_lines.append(lines[i].strip())
            i += 1
        
        current_user_input = "\n".join(user_input_lines).strip()
        
        if i >= len(lines): # 文件结束，只有用户输入没有AI回复
            if current_user_input:
                dialogues.append({
                    "user_input": current_user_input,
                    "assistant_response": "" # 没有对应的AI回复
                })
            break

        # 跳过 "---" 如果存在
        if i < len(lines) and lines[i].strip() == "---":
            i += 1

        # 寻找 AI 回复
        while i < len(lines) and not lines[i].startswith("## 🤖 Assistant"):
            i += 1
        
        if i >= len(lines): # 文件结束或没有更多AI回复
            break
        
        # 跳过 "## 🤖 Assistant"
        i += 1
        # 读取 AI 回复内容
        while i < len(lines) and not (lines[i].startswith("---") or lines[i].startswith("## 🧑‍💻 User")):
            if lines[i].strip():
                assistant_response_lines.append(lines[i].strip())
            i += 1
        
        current_assistant_response = "\n".join(assistant_response_lines).strip()
        
        if current_user_input or current_assistant_response: # 确保不是空轮次
            dialogues.append({
                "user_input": current_user_input,
                "assistant_response": current_assistant_response
            })
        
        # 跳过 "---" 如果存在
        if i < len(lines) and lines[i].strip() == "---":
            i += 1

    return dialogues

def get_dialogue_segment(
    chatlog_dialogues: list[dict], 
    test_point_index: int = None, 
    test_point_user_input: str = None
) -> tuple[list[dict], str, str]:
    """
    根据测试点（索引或用户输入）提取历史对话、当前用户输入和预期AI回复。
    返回 (chat_history, current_user_input, expected_assistant_response)。
    """
    if test_point_index is not None:
        if not (0 <= test_point_index < len(chatlog_dialogues)):
            raise IndexError("test_point_index 超出对话轮次范围。")
        
        history = chatlog_dialogues[:test_point_index]
        current_user_input = chatlog_dialogues[test_point_index]["user_input"]
        expected_assistant_response = chatlog_dialogues[test_point_index]["assistant_response"]
        return history, current_user_input, expected_assistant_response

    elif test_point_user_input:
        for i, dialogue in enumerate(chatlog_dialogues):
            if test_point_user_input in dialogue["user_input"]:
                history = chatlog_dialogues[:i]
                current_user_input = dialogue["user_input"]
                expected_assistant_response = dialogue["assistant_response"]
                return history, current_user_input, expected_assistant_response
        raise ValueError(f"未找到包含 '{test_point_user_input}' 的用户输入。")
    
    raise ValueError("必须提供 test_point_index 或 test_point_user_input。")


def build_messages(instruction: str, knowledge_material: str, chat_history: list[dict], current_user_input: str) -> list:
    """构建发送给 LLM 的消息列表（适用于原始/完整版上下文）"""
    messages = [{"role": "system", "content": instruction}]
    
    # 将知识材料作为系统消息的一部分
    messages.append({"role": "system", "content": f"以下是参考知识材料：\n{knowledge_material}"})

    for msg_pair in chat_history:
        # 将每个对话轮次的用户输入和AI回复添加到消息列表
        messages.append({"role": "user", "content": msg_pair["user_input"].strip()})
        messages.append({"role": "assistant", "content": msg_pair["assistant_response"].strip()})
    
    messages.append({"role": "user", "content": current_user_input.strip()})
    return messages

def build_optimized_messages(
    instruction: str,
    knowledge_material: str,
    simplified_history: list[dict],
    current_user_input: str,
    original_dialogues: list[dict],
    test_point_index: int
) -> list:
    """
    构建优化方案发送给 LLM 的消息列表。
    规则：
    - 使用 simplified_history 作为历史，但**移除最后一轮的简化 AI 回复**（如果存在）。
    - 将上一轮的原始 AI 回复（来自 original_dialogues[test_point_index - 1]）单独附加，
      以保持对话连贯性而不再重复简化版本。
    """
    messages = [{"role": "system", "content": instruction}]
    messages.append({"role": "system", "content": f"以下是参考知识材料：\n{knowledge_material}"})

    # 构建 cleaned_simplified_history：将 simplified_history 转换为逐消息的 role 列表，
    # 并在最后一轮只保留 user 输入，不保留最后一轮的 assistant 简化回复。
    cleaned_msgs = []
    if simplified_history:
        for idx, pair in enumerate(simplified_history):
            # 始终添加用户输入
            user_text = pair.get("user_input", "").strip()
            if user_text:
                cleaned_msgs.append({"role": "user", "content": user_text})
            # 对于不是最后一轮，保留 assistant 简化回复；对于最后一轮，跳过 assistant 回复
            if idx != len(simplified_history) - 1:
                assistant_text = pair.get("assistant_response", "").strip()
                if assistant_text:
                    cleaned_msgs.append({"role": "assistant", "content": assistant_text})
            else:
                # 最后一轮：不添加简化的 assistant 回复（由原始回答替代）
                pass

    # 将清洗后的简化历史添加到 messages
    for m in cleaned_msgs:
        messages.append(m)

    # 添加 AI 最近一轮的原始回答（上一轮的完整原始回复）
    if test_point_index is not None and test_point_index > 0 and original_dialogues:
        prev_idx = test_point_index - 1
        if 0 <= prev_idx < len(original_dialogues):
            last_original_ai_response = original_dialogues[prev_idx].get("assistant_response", "").strip()
            if last_original_ai_response:
                # 将上一轮原始 AI 回复以 assistant 身份注入（保持对话流）
                messages.append({"role": "assistant", "content": f"【上一轮AI原始回复】\n{last_original_ai_response}"})

    # 最后添加当前用户输入
    messages.append({"role": "user", "content": current_user_input.strip()})
    return messages

def call_openai_api(messages: list, model: str, temperature: float = 0.7) -> str:
    """调用 OpenAI API 并返回响应"""
    try:
        chat_completion = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
        )
        return chat_completion.choices[0].message.content
    except openai.APIError as e:
        print(f"OpenAI API 错误: {e}")
        return f"API 调用失败: {e}"

def write_test_result_to_file(
    test_name:str, 
    output_dir: str,
    persona_prompt: str, 
    knowledge_material: str,
    test_history_type: str, # "原始简化版" 或 "原始完整版"
    test_history: list[dict], 
    current_user_input: str,
    original_expected_response: str,
    llm_response: str
):
    """将测试结果写入本地 Markdown 文件。"""
    os.makedirs(output_dir, exist_ok=True)
    filename = os.path.join(output_dir, f"{test_name}_test_result.md")

    # 对长文本进行省略处理
    def truncate_text(text: str, max_len: int = 200) -> str:
        return text if len(text) <= max_len else text[:max_len] + "..."

    content = f"""# 测试结果报告：{test_name}

## 1. 测试概览

*   **测试时间**：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
*   **测试模型**：{MODEL_NAME}
*   **测试历史类型**：{test_history_type}

## 2. 上下文信息

### AI 身份设定 (Instruction)
```
{truncate_text(persona_prompt)}
```

### 知识材料 (Knowledge Material)
```
{truncate_text(knowledge_material)}
```

### 测试历史对话 ({test_history_type}版)
```
"""
    for i, dialogue in enumerate(test_history):
        content += f"User {i+1}: {truncate_text(dialogue['user_input'], 100)}\n"
        content += f"Assistant {i+1}: {truncate_text(dialogue['assistant_response'], 100)}\n"
    content += f"""
```

### 当前用户输入
```
{current_user_input}
```

## 3. 对比结果

### 原始对话中的预期回复 (Original Expected Response)
```
{original_expected_response}
```

### LLM 生成的回复 (LLM Generated Response)
```
{llm_response}
```

## 4. 人工评估建议
请手动比较“原始对话中的预期回复”和“LLM 生成的回复”，评估优化方案的效果，包括：
*   语义连贯性
*   准确性
*   导师风格保持
*   回答完整度
"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"测试结果已写入: {filename}")


def main():
    # 文件路径
    persona_prompt_path= "real_dialogues/persona-prompt.md"
    knowledge_material_path = "real_dialogues/大脑健身房.md"
    original_chatlog_path = "real_dialogues/大脑健身房_chatlog.md"
    simplified_chatlog_path = "real_dialogues/simplified_dialogue_chatlog.md"

    print("加载文件内容...")
    persona_prompt = load_file_content(persona_prompt_path)
    knowledge_material = load_file_content(knowledge_material_path)
    original_chatlog_content = load_file_content(original_chatlog_path)
    simplified_chatlog_content = load_file_content(simplified_chatlog_path)
    
    if not all([persona_prompt, knowledge_material, original_chatlog_content, simplified_chatlog_content]):
        print("未能加载所有必要文件，请检查文件路径和内容。")
        return

    print("解析原始对话记录...")
    original_dialogues = parse_chatlog(original_chatlog_content)
    print(f"原始对话轮次总数: {len(original_dialogues)}")

    print("解析简化版对话记录...")
    simplified_dialogues = parse_chatlog(simplified_chatlog_content)
    print(f"简化对话轮次总数: {len(simplified_dialogues)}")

    # 选择测试点 (例如，第13轮对话，索引为12)
    # 对应用户输入 "运动的时候分泌的多巴胺激活了什么其他通路吗，创造力是把脑子里不同类型的信息交叉联合生成的东西，那运动能增强神经元之间的通路或者让神经元信号传递更好？"
    test_point_index = 8

    try:
        # 获取原始方案的测试数据
        original_history, current_user_input, original_expected_response = get_dialogue_segment(
            original_dialogues, test_point_index=test_point_index
        )
        # 获取优化方案的测试数据 (简化历史应与原始历史的轮次对应，用户输入和预期回复一致)
        simplified_history, _, _ = get_dialogue_segment(
            simplified_dialogues, test_point_index=test_point_index
        )
    except (IndexError, ValueError) as e:
        print(f"获取测试数据失败: {e}")
        return

    # --- 原始方案（全量历史）测试 ---
    print(f"\n--- 原始方案（测试点: 轮次 {test_point_index+1}） ---")
    original_messages_for_llm = build_messages(persona_prompt, knowledge_material, original_history, current_user_input)
    # print("发送给 LLM 的原始方案消息结构:") # 可选：打印消息结构用于调试
    # for msg in original_messages_for_llm:
    #     print(f"  {msg['role']}: {msg['content'][:150]}...")
    
    original_llm_response = call_openai_api(original_messages_for_llm, model=MODEL_NAME)
    print("原始方案 LLM 响应已生成。")

    # --- 优化方案（简化历史）测试 ---
    print(f"\n--- 优化方案（测试点: 轮次 {test_point_index+1}） ---")
    optimized_messages_for_llm = build_optimized_messages(
        persona_prompt,
        knowledge_material,
        simplified_history,
        current_user_input,
        original_dialogues,
        test_point_index
    )
    # print("发送给 LLM 的优化方案消息结构:") # 可选：打印消息结构用于调试
    # for msg in optimized_messages_for_llm:
    #     print(f"  {msg['role']}: {msg['content'][:150]}...")
    
    optimized_llm_response = call_openai_api(optimized_messages_for_llm, model=MODEL_NAME)
    print("优化方案 LLM 响应已生成。")

    # --- 写入结果文件 ---
    output_dir = "test_results"
    test_name_base = f"dialogue_round_{test_point_index + 1}"

    write_test_result_to_file(
        test_name=f"{test_name_base}_original_scheme",
        output_dir=output_dir,
        persona_prompt=persona_prompt,
        knowledge_material=knowledge_material,
        test_history_type="原始完整版",
        test_history=original_history,
        current_user_input=current_user_input,
        original_expected_response=original_expected_response,
        llm_response=original_llm_response
    )

    write_test_result_to_file(
        test_name=f"{test_name_base}_optimized_scheme",
        output_dir=output_dir,
        persona_prompt=persona_prompt,
        knowledge_material=knowledge_material,
        test_history_type="优化简化版",
        test_history=simplified_history,
        current_user_input=current_user_input,
        original_expected_response=original_expected_response,
        llm_response=optimized_llm_response
    )
    
    print("\n所有测试已完成，请检查 'test_results' 文件夹中的报告文件。")

if __name__ == "__main__":
    main()