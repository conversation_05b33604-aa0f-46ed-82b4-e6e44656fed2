# adk_ai_project/memory.py
"""
简单的内存服务（InMemoryMemoryService）示例，用于 demo 阶段。
此实现提供最小的接口：add_knowledge, get_all_knowledge, get_context_text。
在真实的 adk-python 环境中，应使用框架提供的 BaseMemoryService 或 VertexAiMemoryBankService。
"""

from typing import List, Dict, Any
from google.adk.memory.base_memory_service import BaseMemoryService


class InMemoryMemoryService(BaseMemoryService):
    def __init__(self):
        super().__init__()
        self._items: List[str] = []

    def add_knowledge(self, text: str) -> None:
        """
        向内存中添加一条知识文本（原文或摘要）。
        """
        if text:
            self._items.append(text)

    def get_all_knowledge(self) -> List[str]:
        """
        返回所有存储的知识片段（按添加顺序）。
        """
        return list(self._items)

    def get_context_text(self, max_items: int = 5) -> str:
        """
        将若干知识片段合并为一个适合注入到 instruction 或上下文的文本块。
        默认返回最近的 max_items 条记录合并结果。
        """
        if not self._items:
            return ""
        selected = self._items[-max_items:]
        return "\n\n".join(selected)

    # 实现 BaseMemoryService 的抽象方法
    async def add_session_to_memory(
        self, session_id: str, app_name: str,
        user_id: str, session_state: Dict[str, Any],
        memory_items: List[Dict[str, Any]]
    ) -> None:
        # 简化实现，这里只是打印
        print(f"InMemoryMemoryService: Adding session {session_id} to memory.")
        # 实际中可能存储 memory_items 或 session_state

    async def search_memory(
        self, session_id: str, app_name: str,
        user_id: str, query: str,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        # 简化实现，这里返回空列表
        print(f"InMemoryMemoryService: Searching memory for session "
              f"{session_id} with query '{query}'.")
        return []
