# ADK 框架 Token 优化 Demo 开发排障指南

本文档记录了在开发 ADK 框架下 Token 优化 Demo 过程中遇到的主要问题、原因分析以及解决方案，旨在为后续的开发和调试提供参考。

## 1. `google-adk` 包安装及导入路径问题

### 问题描述
*   初期遇到 `ModuleNotFoundError: No module named 'google'` 或 `No module named 'google.adk.runner'`。
*   随后，在导入 `Session`、`State`、`BaseMemoryService` 等模块时，反复遇到 `ModuleNotFoundError`。

### 原因分析
`google-adk` 库的模块结构和导入路径在不同版本中可能存在差异，或者在手动模拟 ADK 框架时，对内部模块的导入路径理解有误。DeepWiki 的查询结果证实了这些导入路径确实发生了变化。

### 解决方案
*   **安装 `google-adk`：** 确保使用 `python3 -m pip install google-adk` 进行安装，并可指定 PyPI 官方源 `https://pypi.org/simple`。
*   **修正导入路径：**
    *   `Session`, `State`: `from google.adk.sessions import Session, State` (而非 `google.adk.session`)
    *   `BaseMemoryService`: `from google.adk.memory.base_memory_service import BaseMemoryService` (而非 `google.adk.services.memory`)
    *   `InMemorySessionService`: `from google.adk.sessions import InMemorySessionService`

## 2. 本地模块相对导入问题

### 问题描述
在 `adk_ai_project/main.py` 中使用 `from .agents import ...` 进行相对导入时，出现 `ImportError: attempted relative import with no known parent package`。

### 原因分析
当 `main.py` 文件被直接作为脚本运行（例如 `python3 adk_ai_project/main.py`）时，Python 解释器不会将其视为一个包的成员，因此无法确定相对导入的父包。

### 解决方案
*   **改为绝对导入：** 将 `from .agents import ...` 改回 `from adk_ai_project.agents import ...`。
*   **以模块形式运行：** 在 `adk_ai_project` 所在的父目录（例如 `little-talk`）中，使用 `python3 -m adk_ai_project.main` 命令来运行程序。这样 Python 解释器会将 `adk_ai_project` 视为一个包，并正确解析内部模块的绝对导入。

## 3. Pydantic 验证错误 - `Session` 类实例化

### 问题描述
尝试实例化 `Session` 类时，出现 `TypeError: State.__init__() missing 2 required positional arguments: 'value' and 'delta'` 或 `pydantic_core._pydantic_core.ValidationError: validation errors for Session`，提示 `id`, `appName`, `userId` 字段缺失，且 `session_id` 是不允许的额外输入。

### 原因分析
`google-adk` 库中的 `Session` 类使用了 Pydantic 进行数据模型验证。
*   `State` 类可能不是直接通过 `State()` 实例化，而是作为 `Session` 的 `state` 属性，通过字典形式初始化。
*   `Session` 类的构造函数需要特定的参数名称 (`id`, `appName`, `userId`)，而不是 `session_id`。

### 解决方案
*   **`State` 初始化：** 在 `Session` 构造函数中，将 `state` 参数直接传入一个字典，例如 `state={"simplified_chat_history": []}`。
*   **`Session` 参数名：** 将 `session_id` 参数替换为 `id`，并添加 `appName` 和 `userId` 参数。
    修改后示例：`session = Session(id="demo_session", appName="demo_app", userId="demo_user", state={"simplified_chat_history": []})`

## 4. Pydantic 验证错误 - `InvocationContext` 类实例化

### 问题描述
尝试实例化 `InvocationContext` 类时，出现 `pydantic_core._pydantic_core.ValidationError: validation errors for InvocationContext`，提示 `session_service`, `invocation_id`, `agent` 字段缺失，并且 `memory_service` 的类型不正确 (`Input should be an instance of BaseMemoryService`)。

### 原因分析
`google-adk` 库中的 `InvocationContext` 类同样使用了 Pydantic 进行数据模型验证，对所有传入参数有严格的类型和存在性要求。
*   `memory_service` 期望接收 `BaseMemoryService` 的实例，而不是一个未继承该基类的自定义类。
*   `InvocationContext` 需要 `session_service` (类型为 `BaseSessionService` 实例)、唯一的 `invocation_id` 和当前执行的 `agent` 实例。

### 解决方案
*   **修改 `adk_ai_project/memory.py`：**
    *   导入 `BaseMemoryService`：`from google.adk.memory.base_memory_service import BaseMemoryService`
    *   让 `InMemoryMemoryService` 继承 `BaseMemoryService`：`class InMemoryMemoryService(BaseMemoryService):`
    *   为 `InMemoryMemoryService` 实现 `BaseMemoryService` 的抽象方法 `add_session_to_memory` 和 `search_memory` (即使是简化实现)。

*   **修改 `adk_ai_project/main.py`：**
    *   导入 `uuid` 模块：`import uuid`
    *   导入 `InMemorySessionService` 和 `BaseSessionService`：`from google.adk.sessions import Session, State, InMemorySessionService` 和 `from google.adk.sessions.base_session_service import BaseSessionService`
    *   实例化 `InMemorySessionService`：`session_service = InMemorySessionService()`
    *   更新 `InvocationContext` 的实例化参数：
        ```python
        ctx = InvocationContext(
            session=session,
            session_service=session_service,
            memory_service=memory_service,
            invocation_id=str(uuid.uuid4()),
            agent=main_agent
        )
        ```
    *   修正 `before_model_callback` 中未定义的 `current_user_input_content`。这应该改为 `types.Content(role="user", parts=[types.Part(text=current_user_input_text)])`。
    *   更新 `CallbackContext` 的实例化参数，确保 `session_service`, `memory_service`, `invocation_context` 均被传递。

通过以上修改，应能解决 `InvocationContext` 的 Pydantic 验证错误。

## 5. `Runner` 类初始化问题：`TypeError: Runner.__init__() got an unexpected keyword argument 'session'`

### 问题描述
尝试运行 `python3 -m adk_ai_project.main` 时，遇到 `TypeError: Runner.__init__() got an unexpected keyword argument 'session'` 错误。

### 原因分析
`google/adk-python` 框架中的 `Runner` 类 `__init__` 方法不直接接受 `Session` 对象作为参数，而是期望一个 `BaseSessionService` 实例来管理会话。`Session` 对象应通过 `session_service` 来创建和管理。我们之前直接将 `Session` 对象作为 `session` 参数传递给 `Runner` 导致了此错误。

### 解决方案
*   **使用 `InMemoryRunner` 或 `Runner` 配合 `BaseSessionService`：**
    *   **推荐使用 `InMemoryRunner`（用于测试和开发）：** `InMemoryRunner` 会自动初始化所有必要的内存服务（包括 `InMemorySessionService`），方便快速启动。
        ```python
        from google.adk.runners import InMemoryRunner
        # ...
        runner = InMemoryRunner(root_agent=main_llm_agent, app_name="DeepMentorApp")
        # 此时可以通过 runner.session_service 访问 InMemorySessionService
        # 并通过 runner.session_service.create_session 创建或获取 Session
        initial_session = await runner.session_service.create_session(
            app_name="DeepMentorApp",
            user_id="DemoUser",
            session_id=str(uuid.uuid4()),
            state={SIMPLIFIED_HISTORY_KEY: []}
        )
        ```
    *   **使用 `Runner` 配合 `InMemorySessionService`（更通用）：** 如果需要更细粒度的控制，可以手动初始化 `InMemorySessionService` 并将其传递给 `Runner`。
        ```python
        from google.adk.runners import Runner
        from google.adk.sessions import InMemorySessionService
        # ...
        memory_service = InMemoryMemoryService() # 假设已正确实现
        session_service = InMemorySessionService()
        
        runner = Runner(
            root_agent=main_llm_agent,
            app_name="DeepMentorApp",
            memory_service=memory_service,
            session_service=session_service
        )
        initial_session = await session_service.create_session(
            app_name="DeepMentorApp",
            user_id="DemoUser",
            session_id=str(uuid.uuid4()),
            state={SIMPLIFIED_HISTORY_KEY: []}
        )
        ```
*   **`Runner.run()` 方法的调用：** `Runner` 的 `run` 方法现在接受 `user_id` 和 `session_id`，而不是 `Session` 对象。它会利用内部的 `session_service` 来管理会话状态。
    ```python
    async for event in runner.run(
        user_id=initial_session.user_id,
        session_id=initial_session.id,
        new_message=user_input
    ):
        # ... 处理事件
    ```
通过以上修改，可以解决 `Runner` 初始化时的 `TypeError`，并正确地使用 ADK 框架的会话管理机制。