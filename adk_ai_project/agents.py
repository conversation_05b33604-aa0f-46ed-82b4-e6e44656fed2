# adk_ai_project/agents.py
"""
定义 ADK 框架下的自定义 Agent 和回调函数。
"""
import logging
from typing import AsyncGenerator, List, Dict, Optional

from google.adk.agents import BaseAgent
from google.adk.agents.callback_context import CallbackContext
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event
from google.adk.models import LlmRequest, LlmResponse

# 配置日志
logger = logging.getLogger(__name__)

# 定义一个用于存储简化历史的会话状态键
SIMPLIFIED_HISTORY_KEY = "simplified_chat_history"


class CompressionSubAgent(BaseAgent):
    """
    负责对话历史增量压缩的子 Agent。
    维护一个最新的、持续更新的简化版对话记录 (`simplified_chat_history`)。
    """

    def __init__(self, name: str, description: str):
        super().__init__(name=name, description=description)

    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        # CompressionSubAgent 主要是被 after_model_callback 调用来执行压缩逻辑，
        # 所以其自身的 _run_async_impl 在 Demo 中可能不会直接被 Runner 调用来
        # 生成 Event。但为了符合 BaseAgent 接口，这里提供一个基本实现。
        logger.info(
            f"CompressionSubAgent '{self.name}' running its _run_async_impl."
        )
        yield Event(type="debug", data="CompressionSubAgent activated.")

    def compress_dialogue_round(
        self, user_input: str, assistant_response: str
    ) -> Dict[str, str]:
        """
        对单个对话轮次进行压缩。
        这里是一个简化的实现，实际中可能需要更复杂的 LLM 调用或规则。
        """
        # 实际的压缩逻辑会更复杂，可能调用一个内部的 LlmAgent
        # 或者使用固定的规则。暂时我们只做简单的拼接或截断。
        user_text = user_input
        if len(user_input) > 100:
            user_text = f"{user_input[:100]}..."
        simplified_user = f"用户: {user_text}"

        assistant_text = assistant_response
        if len(assistant_response) > 100:
            assistant_text = f"{assistant_response[:100]}..."
        simplified_assistant = f"AI: {assistant_text}"
        return {
            "user_input": simplified_user,
            "assistant_response": simplified_assistant,
        }


def before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """
    在 LLM 调用之前触发。记录当前用户输入，供 after_model_callback 使用。
    暂时不修改 llm_request 结构，避免兼容性问题。
    """
    logger.info(
        f"--- 触发 before_model_callback for agent: "
        f"{callback_context.agent_name} ---"
    )

    simplified_history: List[Dict[str, str]] = callback_context.state.get(
        SIMPLIFIED_HISTORY_KEY, []
    )

    # 打印当前简化历史（调试用）
    logger.info(
        f"当前 simplified_chat_history: {len(simplified_history)} 轮对话"
    )

    # 获取当前的用户输入，需要从原始 llm_request.contents 中提取
    current_user_input_content = ""
    if llm_request.contents:
        # 提取当前用户输入并存储到状态中，供 after_model_callback 使用
        for content_part in reversed(llm_request.contents):
            if (
                hasattr(content_part, 'role') and content_part.role == "user"
                and hasattr(content_part, 'parts') and content_part.parts
            ):
                if content_part.parts[0].text:
                    current_user_input_content = content_part.parts[0].text
                    callback_context.state["current_user_input"] = \
                        current_user_input_content
                    logger.info(
                        "在 before_model_callback 中存储当前用户输入: "
                        f"{current_user_input_content[:50]}..."
                    )
                    break

    return None  # 返回 None 允许 LLM 调用继续


def after_model_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    """
    在 LLM 调用之后触发。获取 LLM 响应，调用 CompressionSubAgent 进行压缩，
    并更新会话状态。
    """
    logger.info(
        f"--- 触发 after_model_callback for agent: "
        f"{callback_context.agent_name} ---"
    )

    # 获取当前用户输入（从 before_model_callback 存储的状态中获取）
    current_user_input: str = callback_context.state.get(
        "current_user_input", ""
    )
    llm_generated_response: str = ""
    if llm_response.content and llm_response.content.parts:
        llm_generated_response = llm_response.content.parts[0].text

    logger.debug(
        f"原始用户输入 (after_model_callback): {current_user_input[:50]}..."
    )
    logger.debug(
        f"LLM 原始回复 (after_model_callback): "
        f"{llm_generated_response[:50]}..."
    )

    # 获取当前的简化历史
    simplified_history: List[Dict[str, str]] = callback_context.state.get(
        SIMPLIFIED_HISTORY_KEY, []
    )

    # 创建 CompressionSubAgent 实例进行压缩
    # 在实际应用中，CompressionSubAgent 应该通过 Agent 协作或 LlmAgent 属性传递
    compression_agent = CompressionSubAgent(
        name="CompressionSubAgent", description="Dialog history compressor"
    )
    compressed_round = compression_agent.compress_dialogue_round(
        current_user_input, llm_generated_response
    )

    # 将压缩后的轮次添加到简化历史中
    simplified_history.append(compressed_round)
    logger.info(f"压缩后的对话轮次: {compressed_round}")

    # 更新会话状态
    callback_context.state[SIMPLIFIED_HISTORY_KEY] = simplified_history
    logger.info(
        f"简化历史已更新。当前 simplified_chat_history: {simplified_history}"
    )

    return None  # 返回 None 使用原始 LLM 响应
