# adk_ai_project/agents.py
"""
定义 ADK 框架下的自定义 Agent 和回调函数。
"""
import logging
from typing import AsyncGenerator, List, Dict, Optional

from google.adk.agents import BaseAgent
from google.adk.agents.callback_context import CallbackContext
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event
from google.adk.models import LlmRequest, LlmResponse

# 配置日志
logger = logging.getLogger(__name__)

# 定义一个用于存储简化历史的会话状态键
SIMPLIFIED_HISTORY_KEY = "simplified_chat_history"


class CompressionSubAgent(BaseAgent):
    """
    负责对话历史增量压缩的子 Agent。
    维护一个最新的、持续更新的简化版对话记录 (`simplified_chat_history`)。
    """

    def __init__(self, name: str, description: str):
        super().__init__(name=name, description=description)

    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        # CompressionSubAgent 主要是被 after_model_callback 调用来执行压缩逻辑，
        # 所以其自身的 _run_async_impl 在 Demo 中可能不会直接被 Runner 调用来
        # 生成 Event。但为了符合 BaseAgent 接口，这里提供一个基本实现。
        logger.info(
            f"CompressionSubAgent '{self.name}' running its _run_async_impl."
        )
        yield Event(type="debug", data="CompressionSubAgent activated.")

    async def compress_dialogue_round(
        self,
        user_input: str,
        assistant_response: str,
        simplified_history: List[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """
        使用 LLM 对单个对话轮次进行智能压缩。

        Args:
            user_input: 当前用户输入
            assistant_response: 当前 AI 回复
            simplified_history: 已有的简化历史记录

        Returns:
            包含压缩后用户输入和 AI 回复的字典
        """
        if simplified_history is None:
            simplified_history = []

        # 构建压缩提示词
        compression_prompt = self._build_compression_prompt(
            simplified_history, user_input, assistant_response
        )

        try:
            # 调用 LLM 进行压缩
            compressed_result = await self._call_compression_llm(compression_prompt)

            # 解析 LLM 返回的结果
            parsed_result = self._parse_compression_result(compressed_result)

            return parsed_result

        except Exception as e:
            logger.error(f"LLM 压缩失败，使用简单截断方法: {e}")
            # 如果 LLM 调用失败，回退到简单的截断方法
            return self._simple_compression_fallback(user_input, assistant_response)

    def _simple_compression_fallback(
        self, user_input: str, assistant_response: str
    ) -> Dict[str, str]:
        """简单的压缩回退方法"""
        user_text = user_input
        if len(user_input) > 100:
            user_text = f"{user_input[:100]}..."
        simplified_user = f"用户: {user_text}"

        assistant_text = assistant_response
        if len(assistant_response) > 100:
            assistant_text = f"{assistant_response[:100]}..."
        simplified_assistant = f"AI: {assistant_text}"
        return {
            "user_input": simplified_user,
            "assistant_response": simplified_assistant,
        }

    def _build_compression_prompt(
        self,
        simplified_history: List[Dict[str, str]],
        user_input: str,
        assistant_response: str
    ) -> str:
        """构建压缩提示词"""

        # 基础压缩提示词
        base_prompt = """# 你是一个智能增量对话压缩助手，你的任务是在已有的简化对话历史背景下，对最新一轮的用户提问和AI回复进行高效压缩。

## 任务目标
在不改变已有简化历史的前提下，将最新一轮的用户输入和AI回复压缩为精炼版本，大幅减少token消耗，同时保持其核心信息、逻辑完整性，并确保与历史对话的流畅衔接。

## 压缩原则

### 1. 延续对话形式与风格
- 输出仍应是User和Assistant的对话格式。
- 避免使用描述性语言，如"用户表示..."、"AI确认..."。
- 确保压缩后的内容自然流畅，如同对话的延续。

### 2. 信息增量自包含与衔接
- **核心：** 压缩时需结合已有的"简化历史记录"来理解最新一轮的上下文，但**只对最新一轮的对话内容进行实际压缩**。
- 用户发言要精炼为核心意图或关键信息，必要时补充极简的背景以确保自包含。
- AI发言要保留本轮的核心教学内容、关键比喻（如"大脑肥料"、"啦啦队"等）、逻辑推进点，并确保与用户提问的直接回应关系。
- 保证本轮压缩后的内容能与之前的简化历史无缝衔接，形成完整的、连贯的简化对话流。

### 3. 精准去除冗余
- 识别并删除最新一轮对话中重复的礼貌用语、无关紧要的过渡词汇。
- 合并意思相近的句子，只保留最精炼的表达。
- 在不影响核心信息和逻辑理解的前提下，删除冗余的解释和展开。

### 4. 突出本轮关键信息
- 如果最新一轮对话中包含用户的重要理解突破点，或者引入了新的核心概念，必须予以保留。
- 关注本轮对话的逻辑转折和核心结论。

## 执行指令
请严格遵循上述原则，基于提供的简化历史记录上下文，仅对当前用户输入和AI回复进行增量压缩。输出格式必须为：
User: [压缩后的用户发言]
AI: [压缩后的AI发言]

"""

        # 添加简化历史记录上下文
        if simplified_history:
            history_context = "\n## 简化历史记录 (用于理解上下文，无需修改)\n"
            for i, dialogue in enumerate(simplified_history):
                history_context += f"轮次 {i+1}:\n"
                history_context += f"{dialogue.get('user_input', '')}\n"
                history_context += f"{dialogue.get('assistant_response', '')}\n\n"
        else:
            history_context = "\n## 简化历史记录\n(暂无历史记录)\n\n"

        # 添加当前对话内容
        current_dialogue = f"""## 当前用户输入 (需要压缩)
{user_input}

## 当前AI回复 (需要压缩)
{assistant_response}

## 请输出压缩结果
"""

        return base_prompt + history_context + current_dialogue

    async def _call_compression_llm(self, prompt: str) -> str:
        """调用 LLM 进行压缩"""
        # 这里我们需要创建一个简单的 LLM 调用
        # 使用与主 Agent 相同的 LiteLLM 配置
        from google.adk.models.lite_llm import LiteLlm
        import os

        model_name = os.getenv("MODEL", "gemini-2.5-flash")
        llm = LiteLlm(model=f"openai/{model_name}")

        # 构建请求
        from google.adk.models import LlmRequest
        from google.genai import types

        llm_request = LlmRequest(
            contents=[
                types.Content(role="user", parts=[types.Part(text=prompt)])
            ]
        )

        # 调用 LLM
        response_generator = llm.generate_content_async(llm_request)
        async for response in response_generator:
            if response.content and response.content.parts:
                return response.content.parts[0].text

        raise Exception("LLM 没有返回有效响应")

    def _parse_compression_result(self, llm_response: str) -> Dict[str, str]:
        """解析 LLM 返回的压缩结果"""
        try:
            lines = llm_response.strip().split('\n')
            user_line = ""
            ai_line = ""

            for line in lines:
                line = line.strip()
                if line.startswith("User:") or line.startswith("用户:"):
                    user_line = line
                elif line.startswith("AI:") or line.startswith("Assistant:"):
                    ai_line = line

            if not user_line or not ai_line:
                raise ValueError("无法解析 LLM 响应格式")

            return {
                "user_input": user_line,
                "assistant_response": ai_line,
            }

        except Exception as e:
            logger.error(f"解析压缩结果失败: {e}")
            raise


def before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """
    在 LLM 调用之前触发。记录当前用户输入，供 after_model_callback 使用。
    暂时不修改 llm_request 结构，避免兼容性问题。
    """
    logger.info(
        f"--- 触发 before_model_callback for agent: "
        f"{callback_context.agent_name} ---"
    )

    simplified_history: List[Dict[str, str]] = callback_context.state.get(
        SIMPLIFIED_HISTORY_KEY, []
    )

    # 打印当前简化历史（调试用）
    logger.info(
        f"当前 simplified_chat_history: {len(simplified_history)} 轮对话"
    )

    # 获取当前的用户输入，需要从原始 llm_request.contents 中提取
    current_user_input_content = ""
    if llm_request.contents:
        # 提取当前用户输入并存储到状态中，供 after_model_callback 使用
        for content_part in reversed(llm_request.contents):
            if (
                hasattr(content_part, 'role') and content_part.role == "user"
                and hasattr(content_part, 'parts') and content_part.parts
            ):
                if content_part.parts[0].text:
                    current_user_input_content = content_part.parts[0].text
                    callback_context.state["current_user_input"] = \
                        current_user_input_content
                    logger.info(
                        "在 before_model_callback 中存储当前用户输入: "
                        f"{current_user_input_content[:50]}..."
                    )
                    break

    return None  # 返回 None 允许 LLM 调用继续


def after_model_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    """
    在 LLM 调用之后触发。获取 LLM 响应，调用 CompressionSubAgent 进行压缩，
    并更新会话状态。
    """
    logger.info(
        f"--- 触发 after_model_callback for agent: "
        f"{callback_context.agent_name} ---"
    )

    # 获取当前用户输入（从 before_model_callback 存储的状态中获取）
    current_user_input: str = callback_context.state.get(
        "current_user_input", ""
    )
    llm_generated_response: str = ""
    if llm_response.content and llm_response.content.parts:
        llm_generated_response = llm_response.content.parts[0].text

    logger.debug(
        f"原始用户输入 (after_model_callback): {current_user_input[:50]}..."
    )
    logger.debug(
        f"LLM 原始回复 (after_model_callback): "
        f"{llm_generated_response[:50]}..."
    )

    # 获取当前的简化历史
    simplified_history: List[Dict[str, str]] = callback_context.state.get(
        SIMPLIFIED_HISTORY_KEY, []
    )

    # 创建 CompressionSubAgent 实例进行压缩
    # 在实际应用中，CompressionSubAgent 应该通过 Agent 协作或 LlmAgent 属性传递
    compression_agent = CompressionSubAgent(
        name="CompressionSubAgent", description="Dialog history compressor"
    )
    compressed_round = compression_agent.compress_dialogue_round(
        current_user_input, llm_generated_response
    )

    # 将压缩后的轮次添加到简化历史中
    simplified_history.append(compressed_round)
    logger.info(f"压缩后的对话轮次: {compressed_round}")

    # 更新会话状态
    callback_context.state[SIMPLIFIED_HISTORY_KEY] = simplified_history
    logger.info(
        f"简化历史已更新。当前 simplified_chat_history: {simplified_history}"
    )

    return None  # 返回 None 使用原始 LLM 响应
