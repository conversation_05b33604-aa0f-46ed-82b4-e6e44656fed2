# adk_ai_project/agents.py
"""
定义 ADK 框架下的自定义 Agent 和回调函数。
"""
import logging
from typing import AsyncGenerator, List, Dict, Optional

from google.adk.agents import BaseAgent
from google.adk.agents.callback_context import CallbackContext
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event
from google.adk.models import LlmRequest, LlmResponse
from google.genai import types

# 配置日志
logger = logging.getLogger(__name__)

# 定义一个用于存储简化历史的会话状态键
SIMPLIFIED_HISTORY_KEY = "simplified_chat_history"


class CompressionSubAgent(BaseAgent):
    """
    负责对话历史增量压缩的子 Agent。
    维护一个最新的、持续更新的简化版对话记录 (`simplified_chat_history`)。
    """

    def __init__(self, name: str, description: str):
        super().__init__(name=name, description=description)

    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        # CompressionSubAgent 主要是被 after_model_callback 调用来执行压缩逻辑，
        # 所以其自身的 _run_async_impl 在 Demo 中可能不会直接被 Runner 调用来
        # 生成 Event。但为了符合 BaseAgent 接口，这里提供一个基本实现。
        logger.info(
            f"CompressionSubAgent '{self.name}' running its _run_async_impl."
        )
        yield Event(type="debug", data="CompressionSubAgent activated.")

    def compress_dialogue_round(
        self, user_input: str, assistant_response: str
    ) -> Dict[str, str]:
        """
        对单个对话轮次进行压缩。
        这里是一个简化的实现，实际中可能需要更复杂的 LLM 调用或规则。
        """
        # 实际的压缩逻辑会更复杂，可能调用一个内部的 LlmAgent
        # 或者使用固定的规则。暂时我们只做简单的拼接或截断。
        simplified_user = (
            f"用户: {user_input[:100]}..."
            if len(user_input) > 100
            else f"用户: {user_input}"
        )
        simplified_assistant = (
            f"AI: {assistant_response[:100]}..."
            if len(assistant_response) > 100
            else f"AI: {assistant_response}"
        )
        return {
            "user_input": simplified_user,
            "assistant_response": simplified_assistant,
        }


def before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """
    在 LLM 调用之前触发。从会话状态中读取简化历史并注入到 LlmRequest 中。
    """
    logger.info(
        f"--- 触发 before_model_callback for agent: "
        f"{callback_context.agent_name} ---"
    )

    simplified_history: List[Dict[str, str]] = callback_context.state.get(
        SIMPLIFIED_HISTORY_KEY, []
    )

    # 打印即将发送的简化历史（调试用）
    logger.debug(
        f"当前 simplified_chat_history (before_model_callback): "
        f"{simplified_history}"
    )

    # 获取当前的用户输入，需要从原始 llm_request.contents 中提取
    current_user_input = ""
    if llm_request.contents:
        # 提取当前用户输入并存储到状态中，供 after_model_callback 使用
        for content_part in reversed(llm_request.contents):
            if (
                isinstance(content_part, types.Content)
                and content_part.role == "user"
            ):
                if content_part.parts and content_part.parts[0].text:
                    current_user_input = content_part.parts[0].text
                    callback_context.state[
                        "current_user_input_for_compression"
                    ] = current_user_input
                    logger.info(
                        "在 before_model_callback 中存储当前用户输入: "
                        f"{current_user_input[:50]}..."
                    )
                    break

    # 构造新的 messages 列表，只包含 system_instruction, knowledge,
    # simplified_history 和 current_user_input

    # 在 ADK 框架中，llm_request.contents 会自动构建历史。
    # 我们这里的目标是替换掉默认的完整历史，注入简化历史。
    # 因此，我们先清空 llm_request.contents，然后按需添加。
    new_contents: List[types.Content] = []

    # 1. 添加系统指令 (instruction)
    if llm_request.config.system_instruction:
        new_contents.append(llm_request.config.system_instruction)

    # 2. 将知识材料作为系统消息的一部分（如果 MemoryService 中有的话）
    # 在 Demo 中，我们将知识材料直接从文件加载并注入到 instruction
    # 或者作为一个单独的 system 消息
    # 这里假设知识材料已经以某种方式（例如在 Runner 初始化时）被合并到
    # instruction 中，或者我们可以在此处从某个地方读取并添加。
    # 为了简化，暂时假设 knowledge_material 也会被处理成 system_instruction

    # 将简化历史转换为 types.Content 格式
    for dialogue in simplified_history:
        user_text = dialogue.get("user_input", "").strip()
        assistant_text = dialogue.get("assistant_response", "").strip()
        if user_text:
            new_contents.append(
                types.Content(role="user", parts=[types.Part(text=user_text)])
            )
        if assistant_text:
            new_contents.append(
                types.Content(
                    role="assistant", parts=[types.Part(text=assistant_text)]
                )
            )

    # 3. 添加当前用户输入 (原始形式)
    if current_user_input:  # 检查是否有用户输入
        new_contents.append(
            types.Content(role="user", parts=[types.Part(text=current_user_input)])
        )
        logger.info(f"注入当前用户输入: {current_user_input[:50]}...")

    # 更新 llm_request.contents
    llm_request.contents = new_contents
    logger.info(
        "LLMRequest.contents 已更新为简化历史和当前用户输入。 "
        f"新的消息数量: {len(llm_request.contents)}"
    )

    # 调试：打印最终发送给 LLM 的消息结构 (前几条)
    for i, msg in enumerate(llm_request.contents[:5]):
        if hasattr(msg, 'role') and hasattr(msg, 'parts') and msg.parts:
            logger.debug(
                f"  Final Msg {i+1} ({msg.role}): {msg.parts[0].text[:100]}..."
            )
        else:
            logger.debug(f"  Final Msg {i+1}: {type(msg)} - {str(msg)[:100]}...")

    return None  # 返回 None 允许 LLM 调用继续


def after_model_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    """
    在 LLM 调用之后触发。获取 LLM 响应，调用 CompressionSubAgent 进行压缩，
    并更新会话状态。
    """
    logger.info(
        "--- 触发 after_model_callback for agent: "
        f"{callback_context.agent_name} ---"
    )

    # 获取当前用户输入（从 before_model_callback 存储的状态中获取）
    current_user_input: str = callback_context.state.get(
        "current_user_input_for_compression", ""
    )
    llm_generated_response: str = (
        llm_response.content.parts[0].text
        if llm_response.content and llm_response.content.parts
        else "")

    logger.debug(
        f"原始用户输入 (after_model_callback): {current_user_input[:50]}..."
    )
    logger.debug(
        f"LLM 原始回复 (after_model_callback): "
        f"{llm_generated_response[:50]}..."
    )

    # 获取当前的简化历史
    simplified_history: List[Dict[str, str]] = callback_context.state.get(
        SIMPLIFIED_HISTORY_KEY, []
    )

    # 创建 CompressionSubAgent 实例进行压缩
    # 在实际应用中，CompressionSubAgent 应该通过 Agent 协作或 LlmAgent 属性传递
    compression_agent = CompressionSubAgent(
        name="CompressionSubAgent", description="Dialog history compressor"
    )
    compressed_round = compression_agent.compress_dialogue_round(
        current_user_input, llm_generated_response
    )

    # 将压缩后的轮次添加到简化历史中
    simplified_history.append(compressed_round)
    logger.info(f"压缩后的对话轮次: {compressed_round}")

    # 更新会话状态
    callback_context.state[SIMPLIFIED_HISTORY_KEY] = simplified_history
    logger.info(
        f"简化历史已更新。当前 simplified_chat_history: {simplified_history}"
    )

    return None  # 返回 None 使用原始 LLM 响应
