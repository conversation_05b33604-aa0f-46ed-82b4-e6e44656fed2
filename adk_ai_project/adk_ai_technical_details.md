# ADK 对话 AI 技术与实施细节文档

## 1. 项目背景与目标

本项目旨在基于 `adk-python` 框架，实现一个前台用户感知不到差异，但后台经过深度优化的“知深导师”对话 AI。核心优化目标是显著减少对话历史的 Token 消耗，同时保持高质量的对话体验和语义连贯性。

## 2. ADK 框架下的关键技术点

### 2.1 Agent 协作与 CompressionSubAgent 的构建

为了实现对话历史的增量压缩，我们将构建一个 `CompressionSubAgent`。

*   **实现方式**: `CompressionSubAgent` 将被实现为一个 `Custom Agent`，继承自 `google.adk.agents.BaseAgent`，并重写其核心异步执行逻辑方法 `_run_async_impl`。`_run_async_impl` 方法是异步生成器，它接收一个 `InvocationContext` 对象 `ctx`，并产出 `Event` 对象。
    ```python
    from google.adk.agents import BaseAgent, LlmAgent
    from google.adk.agents.invocation_context import InvocationContext
    from google.adk.events import Event
    from typing import AsyncGenerator

    class MyCustomAgent(BaseAgent):
        def __init__(self, name: str, description: str, my_llm_agent: LlmAgent):
            super().__init__(name=name, description=description, sub_agents=[my_llm_agent]) # 注册子代理
            self.my_llm_agent = my_llm_agent

        async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
            # 访问会话状态
            current_state = ctx.session.state.get("my_custom_key", "default_value")
            print(f"Custom Agent '{self.name}' running. Current state: {current_state}")

            # 调用子代理并产出事件
            async for event in self.my_llm_agent.run_async(ctx):
                yield event

            # 更新会话状态
            ctx.session.state["my_custom_key"] = "updated_value_from_custom_agent"
            print(f"Custom Agent '{self.name}' finished. State updated.")
    ```
*   **核心职责**:
    *   维护一个最新的、持续更新的简化版对话记录 (`simplified_chat_history`)。
    *   在每轮对话结束后，接收最新的用户原始发言和 AI 原始回复。
    *   依据预定义的压缩原则（例如保持对话形式、信息自包含、去除冗余、突出关键节点），对最新一轮的发言内容进行分析、提炼和简化。
    *   将压缩后的内容智能地合并或追加到 `simplified_chat_history` 中，确保其始终保持精简和最新。
*   **状态管理**: `CompressionSubAgent` 将利用 `ctx.session.state` 来存储和更新 `simplified_chat_history`。这将确保压缩后的历史记录在 Agent 之间共享和持久化。
    *   **访问方式**: 在 `_run_async_impl` 中，通过 `ctx.session.state` 直接访问。
    *   **最佳实践**: 避免修改宽泛的结构，使用具体的键来存储数据，以防止意外的副作用。对于持久化存储，考虑使用 `State.APP_PREFIX`、`State.USER_PREFIX`、`State.TEMP_PREFIX` 等前缀来提高清晰度。
*   **子 Agent 列表**: 在初始化主 Agent 时，`CompressionSubAgent` 将作为其 `sub_agents` 列表的一部分传递，以确立其在 Agent 层次结构中的位置。
*   **`AgentTool` 的调用方式**: `AgentTool` 允许一个代理调用另一个代理作为工具。虽然 DeepWiki 的代码片段中没有直接展示 `AgentTool` 的调用示例，但其调用方式与 `FunctionTool` 类似，都是通过 `LlmAgent` 的 `tools` 参数进行配置。
    *   **调用流程**:
        1.  将 `AgentTool` 实例添加到 `LlmAgent` 的 `tools` 列表中。
        2.  LLM 在其响应中生成一个函数调用，指定 `AgentTool` 及其参数。
        3.  ADK 框架捕获此函数调用并执行相应的 `AgentTool`，从而触发被调用代理的 `run_async` 方法。

### 2.2 回调机制集成压缩逻辑

ADK 的回调机制是无缝集成压缩逻辑的关键。我们将利用 `after_model_callback` 和 `before_model_callback`。

**注册方式:**
在创建 `LlmAgent` 实例时，通过 `before_model_callback` 和 `after_model_callback` 参数传入相应的回调函数。

**回调函数签名:**
*   `before_model_callback`: `def my_before_model_logic(callback_context: CallbackContext, llm_request: LlmRequest) -> Optional[LlmResponse]:`
*   `after_model_callback`: `def my_after_model_logic(callback_context: CallbackContext, llm_response: LlmResponse) -> Optional[LlmResponse]:`

**访问 `callback_context.state`:**
在回调函数中，您可以通过 `callback_context.state` 访问和修改会话状态。

**代码示例:**
```python
from google.adk.agents import LlmAgent
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmResponse, LlmRequest
from google.genai import types
from typing import Optional

def my_before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    print(f"Before model call for agent: {callback_context.agent_name}")
    # 访问并修改状态
    call_count = callback_context.state.get("llm_calls_count", 0)
    callback_context.state["llm_calls_count"] = call_count + 1
    print(f"LLM call count: {callback_context.state['llm_calls_count']}")

    # 示例：修改LLM请求的instruction
    if "add_prefix" in callback_context.state:
        original_instruction = llm_request.config.system_instruction or types.Content(role="system", parts=[])
        new_instruction_text = "[Modified by Callback] " + (original_instruction.parts[0].text if original_instruction.parts else "")
        llm_request.config.system_instruction = types.Content(role="system", parts=[types.Part(text=new_instruction_text)])
        print("LLM instruction modified by callback.")

    # 返回None允许LLM调用继续
    return None

def my_after_model_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    print(f"After model call for agent: {callback_context.agent_name}")
    # 访问状态
    print(f"Current LLM call count: {callback_context.state.get('llm_calls_count')}")

    # 示例：修改LLM响应
    if llm_response.content and llm_response.content.parts and llm_response.content.parts[0].text:
        original_text = llm_response.content.parts[0].text
        if "modify_response" in callback_context.state:
            new_text = original_text + "\n(Modified by after_model_callback)"
            new_response = LlmResponse(content=types.Content(parts=[types.Part(text=new_text)]))
            print("LLM response modified by callback.")
            return new_response # 返回新的LlmResponse以替换原始响应
    # 返回None使用原始LLM响应
    return None

my_llm_agent = LlmAgent(
    name="MyLlmAgentWithCallbacks",
    model="gemini-2.0-flash",
    instruction="You are a helpful assistant.",
    before_model_callback=my_before_model_callback,
    after_model_callback=my_after_model_callback
)
```

*   **`after_model_callback`**: 在 `LlmAgent` 内部向 LLM 发送请求并接收到响应之后触发。
    *   **用途**: 在此回调中，我们将获取 `LlmResponse` 对象，并调用 `CompressionSubAgent` 来处理当前的对话历史（包括用户最新输入和 AI 最新回复），并更新 `simplified_chat_history` 到 `callback_context.state`。这将确保 `simplified_chat_history` 始终保持最新。
    *   **状态更新**: 通过 `callback_context.state` 访问和修改会话状态，确保 `simplified_chat_history` 被正确跟踪和持久化。
        *   **访问方式**: 在回调函数中，通过 `callback_context.state` 访问。
        *   **最佳实践**: 避免修改宽泛的结构，使用具体的键来存储数据，以防止意外的副作用。对于持久化存储，考虑使用 `State.APP_PREFIX`、`State.USER_PREFIX`、`State.TEMP_PREFIX` 等前缀来提高清晰度。

*   **`before_model_callback`**: 在 LLM 调用之前触发。
    *   **用途**: 在此回调中，我们将从 `callback_context.state` 中读取 `simplified_chat_history`，并将其注入到 `llm_request.contents` 或 `llm_request.config.system_instruction` 中，作为 LLM 的上下文，从而减少发送给 LLM 的 Token 数量。这将确保 LLM 在每次决策时都能基于最新的、高效的上下文。

### 2.3 Token 消耗优化

Token 消耗优化通过 `CompressionSubAgent` 对话历史增量压缩和在 `before_model_callback` 中注入简化历史实现。

*   **增量压缩**: `CompressionSubAgent` 仅对最新一轮的对话内容进行压缩，避免了对整个历史的重复处理。
*   **简化历史注入**: 在每次 LLM 调用时，只传递 `simplified_chat_history` 而非完整的对话历史，大幅减少 Token 使用量。

### 2.4 记忆服务集成

我们将集成知识材料，以提供“知深导师”所需的专业知识。

*   **`MemoryService` 或 `KnowledgeBaseTool`**: ADK 提供了 `BaseMemoryService` 抽象和 `VertexAiMemoryBankService` 实现。
    *   **初始化**: `MemoryService` 的实例通常在 `Runner` 初始化时提供。例如，可以使用 `InMemoryMemoryService` 进行测试和原型开发。
    *   **配置**: 可以在 `Runner` 初始化时配置 `MemoryService`。
    *   **使用**: 在 Agent 的 `_run_async_impl` 方法中，可以通过 `ctx.memory_service` 访问记忆服务。
    *   **初期方案**: 为了专注于对话压缩，记忆服务层将保持架构，但暂时不进行复杂的 RAG 处理。知识材料会作为 `LlmAgent` 的 `instruction` 的一部分或额外上下文，在每一轮发送给 LLM 时被包含。
    *   **未来扩展**: 将探讨引入 `VertexAiRagMemoryService` 或自定义 `RetrievalTool` 来实现基于 RAG 的知识检索，并优化 RAG 的 Token 使用。

## 3. 实施细节与注意事项

*   **`simplified_chat_history` 的键名一致性**: 确保 `simplified_chat_history` 在 `ctx.session.state` 中的键名在所有回调和 Agent 逻辑中保持一致，以便正确存取。
*   **异步操作**: ADK 中的操作多为异步。在实现自定义逻辑时，务必遵循异步编程范式。
*   **错误处理**: 在回调和自定义 Agent 逻辑中，应包含适当的错误处理机制，确保 AI 的健壮性。
*   **Agent 层次结构**: 明确 `CompressionSubAgent` 在主 `LlmAgent` 中的位置，确保 ADK 框架能够正确识别和调用。
*   **提示词工程**: 针对 `CompressionSubAgent` 和主 `LlmAgent` 设计高效的提示词，以引导其行为，确保压缩质量和对话连贯性。

## 4. 上下文构建细节

LLM 接收到的 `LlmRequest` 将包含以下关键上下文信息：

*   **Agent 的身份设定 (`instruction`)**: AI Agent 的核心身份（例如“知深学习导师”及其教学哲学）将作为 `LlmAgent` 的 `instruction` 参数的一部分进行明确配置。这确保了 Agent 始终以设定的角色进行交互，并且 `instruction` 也将包含对 Agent 行为模式的引导，例如在接收到压缩后的历史对话时应如何理解和响应。
    *   **配置方式**: 在 `LlmAgent` 的构造函数中直接传入 `instruction` 参数。
    *   **示例**:
        ```python
        from google.adk.agents import LlmAgent

        my_llm_agent = LlmAgent(
            name="MyInstructionAgent",
            model="gemini-2.0-flash",
            instruction="你是一个专业的翻译助手，请将所有用户输入翻译成中文。", # 配置 instruction
            tools=[]
        )
        ```
*   **精简后的历史对话记录**: 通过 `CompressionSubAgent` 进行增量压缩并维护的 `simplified_chat_history`。在 `before_model_callback` 中，它将被注入到 `llm_request.contents` 或 `llm_request.config.system_instruction` 中。
    *   `LLMRequest` 内部会根据会话历史自动构建聊天历史。通常不需要手动注入 `simplified_chat_history`。
    *   如果需要在 `before_model_callback` 中修改聊天历史，可以操作 `llm_request.contents` 列表。
*   **知识材料**: (Demo 阶段) 知识材料（例如《大脑健身房》的原文精简版或摘要）会作为 `LlmAgent` 的 `instruction` 的一部分或额外上下文，在每一轮发送给 LLM 时被包含。
*   **当前的原始用户输入**: 尽管对话历史被压缩，但当前的用户输入将以原始形式（或未来根据需要进行初步预处理）直接传递给 LLM，确保 LLM 能够处理最新的用户意图。

这种构建上下文的方式，确保了 LLM 在每次决策时，都能基于一个精炼、高效且包含所有必要信息的上下文，从而在大幅减少 Token 消耗的同时，保持高质量的对话能力。