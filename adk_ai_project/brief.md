# 项目简介 (Brief)

## 1. 项目名称

ADK 知深导师 AI (ADK Deep Mentor AI)

## 2. 项目背景

随着大型语言模型 (LLM) 技术的发展，对话式 AI 应用日益普及。然而，现有对话 AI 系统在处理多轮复杂对话时，面临 Token 消耗高、成本增加、延迟加剧以及上下文窗口限制等问题。特别是在需要维持长期、连贯且知识丰富的“导师”角色对话中，这些问题尤为突出。

本项目基于当前团队在 ADK Agent Token 优化方案的初步探索成果，旨在进一步实践和落地一个在用户体验（前台）无感知差异，但后台通过创新技术（特别是对话历史压缩）实现显著优化的对话 AI。

## 3. 项目目标

*   **技术目标**:
    *   利用 `adk-python` 框架的模块化和回调机制，实现一个高效的 `CompressionSubAgent`，对对话历史进行增量压缩。
    *   在保证对话质量和语义连贯性的前提下，将对话历史的 Token 消耗至少降低 X%（具体百分比待定量）。
    *   无缝集成记忆服务（初期模拟，未来 RAG 增强），确保“知深导师”能够利用知识材料进行高质量对话。
    *   构建健壮的 Agent 协作机制和回调系统，确保系统稳定运行。
*   **用户体验目标**:
    *   前台用户感知不到因后台优化（如 Token 压缩）带来的任何延迟或功能差异。
    *   “知深导师” AI 能够以费曼学习法为核心哲学，提供自然、动态、深度且个性化的教学体验。
    *   对话流畅、连贯，能够有效引导用户从“熟知”到“真知”。
*   **业务目标**:
    *   显著降低 LLM API 调用成本。
    *   提升对话 AI 系统的可扩展性和长期运行的稳定性。

## 4. 核心功能概述

*   **多轮对话管理**: 维护对话上下文，支持多轮交互。
*   **知深导师角色**: 基于预设的 `persona-prompt`，扮演深度学习导师角色，采用费曼学习法。
*   **对话历史压缩**: 后台自动对用户和 AI 的历史对话进行增量压缩，生成 `simplified_chat_history`。
*   **Token 优化**: 在每次 LLM 调用前，将压缩后的对话历史注入 `LlmRequest`，以减少 Token 消耗。
*   **知识材料集成**: 集成记忆服务，为导师提供专业的知识背景。
*   **前台体验无感知**: 确保后台的复杂优化对前台用户透明。

## 5. 技术栈

*   **核心框架**: `adk-python`
*   **大语言模型 (LLM)**: Gemini (或其他兼容 LiteLlm 的模型)
*   **编程语言**: Python
*   **记忆服务**: ADK `MemoryService` (初期模拟，未来 `VertexAiRagMemoryService` 或自定义 `RetrievalTool`)

## 6. 验证与评估

*   **Token 消耗对比**: 量化分析压缩前后每一轮对话的 Token 消耗。
*   **对话质量评估**: 采用人工评估（语义连贯性、准确性、导师风格保持、回答完整度）和可能引入的 ADK 内置 `Evaluation Framework` 进行系统评估。
*   **用户满意度**: 确保用户在使用过程中无延迟、无功能缺失感知。

## 7. 成功标准

*   Token 消耗显著降低（具体指标待定）。
*   对话质量与非优化方案相当或更优。
*   系统运行稳定，无明显 bug。
*   用户对“知深导师”的教学效果和体验满意。