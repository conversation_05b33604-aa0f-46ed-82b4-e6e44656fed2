# adk_ai_project/main.py
"""
ADK 知深导师 AI Demo 的主入口文件。
负责初始化 Agent、Runner 并运行对话。
"""
import asyncio
import os
import uuid
from dotenv import load_dotenv

from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import Session

from adk_ai_project.agents import (
    before_model_callback,
    after_model_callback,
    SIMPLIFIED_HISTORY_KEY,
)
from adk_ai_project.memory import InMemoryMemoryService

# 加载环境变量
load_dotenv()

# 从环境中获取模型名称
MODEL_NAME = os.getenv("MODEL", "gemini-1.5-flash")  # 默认使用 gemini-1.5-flash


def load_file_content(filepath: str) -> str:
    """加载文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误: 文件未找到 - {filepath}")
        return ""


async def main():
    # 1. 加载必要的文本内容
    persona_prompt_path = "real_dialogues/persona-prompt.md"
    knowledge_material_path = "real_dialogues/大脑健身房.md"

    persona_prompt = load_file_content(persona_prompt_path)
    knowledge_material = load_file_content(knowledge_material_path)

    if not all([persona_prompt, knowledge_material]):
        print("未能加载所有必要文件，请检查文件路径和内容。")
        return

    # 2. 初始化 InMemoryMemoryService
    # 这里不再将知识材料添加到 memory_service，而是直接传入 LlmAgent 的 instruction
    memory_service = InMemoryMemoryService()
    print("InMemoryMemoryService 已初始化。")

    # 3. 初始化 LlmAgent 并注册回调函数
    # 将 persona_prompt 和知识材料结合作为 LLM 的系统指令
    full_instruction = (
        f"{persona_prompt}\n\n以下是参考知识材料：\n{knowledge_material}"
    )

    main_llm_agent = LlmAgent(
        name="DeepMentorAgent",
        model=MODEL_NAME,  # ADK Agent 使用的模型名称
        instruction=full_instruction,
        before_model_callback=before_model_callback,
        after_model_callback=after_model_callback,
    )
    print("LlmAgent 已初始化并注册回调函数。")

    # 4. 初始化 Session
    # 在真实的 ADK Runner 中，这些会话状态是自动管理的。
    # 这里提供一个初始 Session，Runner 会在内部管理其生命周期。
    initial_session = Session(
        id=str(uuid.uuid4()),
        appName="DeepMentorApp",
        userId="DemoUser",
        state={SIMPLIFIED_HISTORY_KEY: []},
    )
    print("初始 Session 已创建。")

    # 5. 初始化 Runner 并运行主 Agent
    runner = Runner(
        session=initial_session,
        root_agent=main_llm_agent,
        memory_service=memory_service,
    )
    print("Runner 已初始化。")

    print("\n--- 开始模拟对话 (输入 'exit' 退出) ---")
    while True:
        user_input = input("用户: ")
        if user_input.lower() == 'exit':
            break

        print("\n--------------------------------------------------")
        print(f"用户输入: {user_input}")
        print("--------------------------------------------------")

        # 调用 Runner 的 run 方法来执行 Agent 链
        # Runner 会负责创建 InvocationContext，并管理回调的执行
        async for event in runner.run(user_input=user_input):
            if event.type == "llm_response":
                print(f"导师: {event.data.content.parts[0].text}")
                print("-" * 30)

        # 打印简化历史，验证状态更新
        current_simplified_history = runner.get_session().state.get(
            SIMPLIFIED_HISTORY_KEY, []
        )
        print(f"当前简化历史 (会话状态): {current_simplified_history}")
        print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
