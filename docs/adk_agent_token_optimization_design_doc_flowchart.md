
```mermaid
graph TD
    A[开始] --> B(用户输入);
    B --> C[OptimizedChatAgent 接收输入];
    C --> D[构建LlmRequest: 简化历史 (不含最近AI简化) + AI最近原始回答 + 用户输入 + AI身份 + 知识材料];
    D --> E[OptimizedChatAgent 发送请求至LLM];
    E --> F[LLM生成原始AI响应];
    F --> G{LLM响应回调触发: on_llm_response_callback};
    G --> H[CompressionSubAgent 接收用户原始输入 + AI原始响应];
    H --> I[CompressionSubAgent 增量压缩内容];
    I --> J[CompressionSubAgent 合并/追加至simplified_chat_history];
    J --> K[OptimizedChatAgent 返回原始AI响应给用户];
    K --> L[simplified_chat_history 更新完成，准备下一轮];
    L --> A;
```

### 流程图说明
本流程图描绘了 ADK Agent Token 优化方案中，对话压缩的核心流程。

1.  **开始**: 流程的起点。
2.  **用户输入**: 用户发起对话，输入内容。
3.  **OptimizedChatAgent 接收输入**: 主 Agent 接收用户的输入。
4.  **构建LlmRequest**: OptimizedChatAgent 组装发送给 LLM 的请求，包含：
    *   当前维护的**简化版历史对话** (`simplified_chat_history`)（**不含最近一轮的简化AI回复**）
    *   **AI 最近一轮的原始回答**
    *   **用户的原始输入**
    *   **AI 的身份设定** (`instruction`)
    *   Agent 所需的**知识材料** (来自记忆服务)
5.  **OptimizedChatAgent 发送请求至LLM**: 将构建好的请求发送给大型语言模型 (LLM)。
6.  **LLM生成原始AI响应**: LLM 根据接收到的上下文和请求，生成原始的 AI 回复。
7.  **LLM响应回调触发**: 在 LLM 生成响应后，`on_llm_response_callback` 被触发，这是一个关键的集成点。
8.  **CompressionSubAgent 接收用户原始输入 + AI原始响应**: 专门的压缩子 Agent 接收到用户和 AI 的原始、未压缩的对话内容。
9.  **CompressionSubAgent 增量压缩内容**: 压缩子 Agent 对这些最新的对话内容进行分析、提炼和简化。
10. **CompressionSubAgent 合并/追加至simplified_chat_history**: 将压缩后的内容智能地合并或追加到 `simplified_chat_history` 中，确保历史记录精简且最新。
11. **OptimizedChatAgent 返回原始AI响应给用户**: 主 Agent 将 LLM 生成的原始 AI 响应返回给用户，保持对话的流畅性。
12. **simplified_chat_history 更新完成，准备下一轮**: 内部的简化版对话历史已更新完毕，系统为下一轮对话做好准备，流程回到开始，等待新的用户输入。
