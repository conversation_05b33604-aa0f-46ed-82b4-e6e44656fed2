## ADK Agent Token 优化方案设计文档 (草稿)

**文档版本**：V0.1 (概念设计)
**文档作者**：AI 导师
**日期**：2025年8月26日

---

### **1. 项目概述**

#### **1.1. 项目目标**

本项目的核心目标是基于 `adk-python` 框架，设计并实现一个能够显著优化 AI 对话过程中 Token 消耗的 Agent 方案。在实现 Token 消耗大幅下降的同时，力求保持与现有（非优化）方案相媲美的对话质量和语义连贯性，并通过后续的 Demo 测试进行效果验证。

#### **1.2. 现有方案痛点分析**

目前的 AI 对话系统在维护上下文时，通常采用“全量传输”模式：每一轮对话都会将 **所有历史对话内容、AI 身份设定以及所有知识材料** 打包发送给 LLM。这种模式存在以下痛点：

*   **高 Token 消耗**：随着对话轮次的增加，每次请求的 Token 量呈线性甚至指数级增长。
*   **成本上升**：Token 消耗直接导致 API 调用成本增加。
*   **延迟增加**：处理大量 Token 会导致 LLM 响应时间变长。
*   **上下文窗口限制**：过长的上下文可能触及 LLM 的上下文窗口上限，导致早期对话信息被截断或遗忘。

#### **1.3. 优化方案核心思路**

本方案提出“对话压缩”的核心优化思路：对每一轮用户和 AI 的发言进行**简化压缩**和**信息提炼**。后续对话中，LLM 接收到的将是这些**简化版发言记录**，结合 Agent 的**身份设定**以及**知识材料**（未来由记忆服务提供），以大幅减少 Token 消耗，同时通过精巧的设计保持对话的语义完整性。

---

### **2. 方案架构与组件设计**

本优化方案将基于 `adk-python` 的模块化特性，引入并集成以下核心组件：

#### **2.1. 主 Agent (`LlmAgent`)**

*   **名称**：`OptimizedChatAgent`
*  **核心职责**：协调整个对话流程，调用各种工具（包括对话压缩工具和记忆服务），并与 LLM 进行最终交互。
*   **身份设定 (`Instruction`)**：
    *   AI Agent的核心身份（例如“知深学习导师”及其教学哲学）将作为 `LlmAgent` 的 `instruction` 参数的**一部分进行明确配置**。这确保了 Agent 始终以设定的角色进行交互。
    *   `instruction` 也将包含对 Agent 行为模式的引导，例如在接收到压缩后的历史对话时应如何理解和响应。

#### **2.2. 对话压缩工具 (`CompressionTool` 或 `CompressionSubAgent`)**

这是本方案的核心创新点。

*   **实现方式**：
    *   **选项 A (`CompressionTool`)**：作为一个独立的 `BaseTool` 子类实现。它将接收一段完整的对话发言（或多段），并返回其压缩版本。
    *   **选项 B (`CompressionSubAgent`)**：设计一个专门的“压缩子Agent”，例如 `CompressionLlmAgent`。主 Agent 通过 `AgentTool` 的方式调用它。这种方式利用了 `adk-python` 的多 Agent 协作能力，可能让压缩逻辑本身更具“智能”和适应性。
    *   **初步选择**：为了充分验证 Agent 协作能力和模块化效果，**初步倾向于使用 `CompressionSubAgent` 的方式**，即主 Agent 调用一个子 Agent 来完成压缩任务。
*   **核心职责**：
    *   **维护简化版对话记录**：`CompressionSubAgent` 或其协调机制将负责维护一个最新的、持续更新的简化版对话记录（`simplified_chat_history`）。
    *   **增量压缩**：在每轮对话结束后，接收**最新的用户原始发言**和**AI 原始回复**（或者仅 AI 原始回复，取决于具体策略）。
    *   **信息提炼**：依据“**AI 对话压缩提示词**”中定义的压缩原则（例如保持对话形式、信息自包含、去除冗余、突出关键节点），对**最新的一轮（或局部）发言内容**进行分析、提炼和简化。
    *   **合并更新**：将压缩后的最新发言内容**智能地合并或追加**到 `simplified_chat_history` 中，确保其始终保持精简和最新。
    *   输出一个**语义上等价但 Token 数量大幅减少**的简化版发言，用于更新 `simplified_chat_history`。
*   **集成方式**：
    *   通过 `adk-python` 的**回调系统 (Callback System)**，特别是 `on_llm_response_callback` 或 `on_aftertool_execute_callback`。
    *   **回调触发时机**：在**每一轮完整的用户与 AI 交互结束，并获得 AI 原始回复之后**触发压缩回调。
    *   **压缩流程**：
        1.  主 `OptimizedChatAgent` 在接收到用户输入后，会将**当前的 `simplified_chat_history`**、**用户的原始输入**、**AI 的身份设定**、以及**知识材料**一并发送给 LLM。
        2.  LLM 基于这些信息生成**原始的 AI 响应**。
        3.  `on_llm_response_callback` 被触发，将**用户原始输入**和**AI 原始响应**传递给 `CompressionSubAgent`。
        4.  `CompressionSubAgent` 对这两部分内容进行**增量压缩**。
        5.  `CompressionSubAgent` 将压缩后的内容**合并或追加**到 `simplified_chat_history` 中。
        6.  主 `OptimizedChatAgent` 将**原始的 AI 响应**返回给用户，同时内部的 `simplified_chat_history` 已完成更新，为下一轮对话做好准备。
    *   **最终传递给 LLM 的 `LlmRequest` 将包含增量更新后的简化历史对话、当前用户输入、Agent 身份设定和知识材料。** 这确保了 LLM 在每次决策时都能基于最新的、高效的上下文。

#### **2.3. 记忆服务层 (`MemoryService` / `KnowledgeBaseTool`)**

*   **核心职责**：管理和提供 Agent 所需的**知识材料**（例如《大脑健身房》的原文）。
*   **初步集成方案 (Demo 阶段)**：
    *   为了在 Demo 阶段专注于验证对话压缩的效果，记忆服务层将**保持其架构**，但暂时**不进行复杂的 RAG 处理**。
    *   知识材料（例如《大脑健身房》的原文精简版或摘要）会作为 `LlmAgent` 的 `instruction` **一部分或额外上下文**，在每一轮发送给 LLM 时被包含。
    *   这模拟了“有知识材料存在”的场景，但简化了知识获取的复杂度，避免了 RAG 本身可能带来的“奇异”行为，让我们能独立评估压缩方案。
*   **未来扩展方向 (后续讨论)**：
    *   将探讨引入 `VertexAiRagMemoryService` 或自定义 `RetrievalTool` 来实现基于 RAG 的知识检索。
    *   详细讨论如何优化 RAG 的查询、匹配和返回结果的 Token 使用。

#### **2.4. LLM 集成**

*   **模型选择**：`adk-python` 支持 `Gemini` 和 `LiteLlm`，可根据测试需求灵活配置。
*   **上下文构建**：LLM 接收到的 `LlmRequest` 将包含：
    *   Agent 的**身份设定** (`instruction`)。
    *   精简后的**历史对话记录**。
    *   **知识材料**（Demo 阶段为原文，未来为 RAG 结果）。
    *   当前的**用户输入**（如果已压缩则为压缩版，否则为原始版）。

---

### **3. 方案验证与测试考量 (Demo 阶段)**

#### **3.1. 验证目标**

*   **Token 消耗对比**：量化压缩前后每一轮对话的 Token 消耗量，验证 Token 节省效果。
*   **对话质量对比**：评估压缩方案下 Agent 对话的语义连贯性、准确性和流畅性，与非压缩方案进行主观和客观对比（例如，与简化前的原始对话日志对比）。
*   **Agent 行为稳定性**：确保压缩过程不会引入“奇异”或非预期的 Agent 行为。

#### **3.2. 测试方法**

*   **交互式测试**：使用 `adk run` 命令行工具或 `adk web` 开发 UI 进行多轮对话，观察 Agent 响应。
*   **日志分析**：记录每轮对话的原始输入、压缩后内容和 LLM 响应，以及对应的 Token 使用量。

---

### **4. 后续工作与未来讨论**

*   **记忆服务详细设计**：一旦对话压缩机制得到初步验证，我们将详细讨论并设计记忆服务（RAG）的具体实现方案及其在 Token 优化中的角色。
*   **更复杂的压缩策略**：探讨动态调整压缩程度、多维度信息提炼（例如情绪、意图提取）的可能性。
*   **评估指标与工具**：研究 `adk-python` 内置的 `Evaluation Framework`，用于更系统地评估对话质量。